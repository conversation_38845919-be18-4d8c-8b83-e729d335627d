"use client";

import {
  Dialog,
  DialogContent,
  DialogTrigger,
  VideoIframe,
} from "@relume_io/relume-ui";
import React from "react";
import { FaCirclePlay } from "react-icons/fa6";

export function Layout355() {
  return (
    <section id="relume" className="relative">
      <div className="px-[5%]">
        <div className="relative z-10 container">
          <div className="grid auto-cols-fr grid-cols-1 pb-8 md:grid-cols-[1fr_10rem_1fr] md:pb-0 lg:grid-cols-[1fr_12rem_1fr]">
            <div className="relative md:mt-[100vh]">
              <div className="flex flex-col justify-center py-8 md:h-screen md:py-0">
                <div className="w-full max-w-sm">
                  <h1 className="mb-5 text-4xl leading-[1.2] font-bold text-text-alternative md:mb-6 md:text-5xl lg:text-6xl">
                    Samen maken we duurza<PERSON>heid tastbaar.
                  </h1>
                  <p className="text-text-alternative md:text-md">
                    Klinkt mooi, maar we dóen het ook. Samen met jullie,
                    bedrijven groot en klein. En dan hebben we het over
                    duurzaamheid in al z’n vormen. Over impact maken. Op alle
                    mogelijke manieren. Dat is de meerwaarde van ons ecosysteem.
                    We rollen de mouwen op om samen lokaal natuur te creëren en
                    jouw ESG-verhaal tastbaar te maken. Om jouw bedrijf een stem
                    te geven, zodat je anderen kan inspireren. En om de
                    betrokkenheid van jouw stakeholders te vergroten door ze
                    slim én leuk te verbinden. Zorgen voor daadkracht. En
                    draagvlak creëren. Inspireren en verbinden. Dat is wat we
                    doen!
                  </p>
                </div>
              </div>
              <div className="flex flex-col justify-center py-8 md:h-screen md:py-0">
                <div className="w-full max-w-sm">
                  <h1 className="mb-5 text-4xl leading-[1.2] font-bold text-text-alternative md:mb-6 md:text-5xl lg:text-6xl">
                    Walk the talk, zeggen ze. En impact maken, dat ook.
                    Makkelijker gezegd dan gedaan? Wij zijn er voor jou.
                  </h1>
                </div>
              </div>
            </div>
            <div className="static top-0 order-first flex h-[50vh] items-center justify-center md:sticky md:order-none md:h-screen">
              <Dialog>
                <DialogTrigger className="absolute z-20 flex items-center justify-center text-white">
                  <span className="flex size-20 flex-col items-center justify-center">
                    <FaCirclePlay className="absolute z-20 size-16 text-white" />
                  </span>
                </DialogTrigger>
                <DialogContent>
                  <VideoIframe video="https://www.youtube.com/embed/8DKLYsikxTs?si=Ch9W0KrDWWUiCMMW" />
                </DialogContent>
              </Dialog>
            </div>
            <div className="relative md:pt-[150vh]">
              <div className="flex flex-col justify-center py-8 md:h-screen md:py-0">
                <div className="w-full max-w-sm">
                  <h1 className="mb-5 text-4xl leading-[1.2] font-bold text-text-alternative md:mb-6 md:text-5xl lg:text-6xl">
                    TO DELETE
                  </h1>
                </div>
              </div>
            </div>
          </div>
          <div className="mb-[-100vh]" />
        </div>
      </div>
      <div className="sticky bottom-0 z-0 h-screen w-screen">
        <div className="absolute inset-0 z-10 bg-black/50" />
        <div className="sticky bottom-0 h-screen w-screen overflow-hidden object-cover">
          <video
            autoPlay={true}
            loop={true}
            muted={true}
            playsInline={true}
            className="absolute -top-full -right-full -bottom-full -left-full m-auto size-full bg-cover [background-position:50%] object-cover"
          >
            <source
              src="https://d22po4pjz3o32e.cloudfront.net/placeholder-video.mp4"
              type="video/mp4"
            />
          </video>
        </div>
      </div>
    </section>
  );
}
