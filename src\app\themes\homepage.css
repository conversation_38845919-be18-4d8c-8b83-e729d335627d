/* Homepage Theme - Default/General Company Colors */
.theme-homepage {
  /* Background Colors */
  --color-background: #ffffff;
  --color-background-primary: #f8fafc;
  --color-background-secondary: #f1f5f9;
  --color-background-tertiary: #e2e8f0;
  --color-background-alternative: #1e293b;

  /* Border Colors */
  --color-border: #e2e8f0;
  --color-border-primary: #cbd5e1;
  --color-border-secondary: #94a3b8;
  --color-border-tertiary: #64748b;
  --color-border-alternative: #475569;

  /* Text Colors */
  --color-text: #0f172a;
  --color-text-primary: #1e293b;
  --color-text-secondary: #475569;
  --color-text-alternative: #ffffff;

  /* Link Colors */
  --color-link: #3b82f6;
  --color-link-primary: #2563eb;
  --color-link-secondary: #1d4ed8;
  --color-link-alternative: #60a5fa;
}

/* Set homepage as default theme */
:root {
  /* Background Colors */
  --color-background: #ffffff;
  --color-background-primary: #f8fafc;
  --color-background-secondary: #f1f5f9;
  --color-background-tertiary: #e2e8f0;
  --color-background-alternative: #1e293b;

  /* Border Colors */
  --color-border: #e2e8f0;
  --color-border-primary: #cbd5e1;
  --color-border-secondary: #94a3b8;
  --color-border-tertiary: #64748b;
  --color-border-alternative: #475569;

  /* Text Colors */
  --color-text: #0f172a;
  --color-text-primary: #1e293b;
  --color-text-secondary: #475569;
  --color-text-alternative: #ffffff;

  /* Link Colors */
  --color-link: #3b82f6;
  --color-link-primary: #2563eb;
  --color-link-secondary: #1d4ed8;
  --color-link-alternative: #60a5fa;
}
