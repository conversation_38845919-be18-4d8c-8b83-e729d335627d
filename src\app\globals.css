/* styles/globals.css */

/* Import theme files - must come before @tailwind directives */
@import './themes/homepage.css';
@import './themes/forestforward.css';
@import './themes/lagom.css';
@import './themes/storyforward.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base font styles - ensure all text uses Poppins */
@layer base {
  /* Set default body font to Poppins */
  body {
    font-family: var(--font-heading), 'Poppins', sans-serif;
  }

  /* Set all headings to use Poppins */
  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading), 'Poppins', sans-serif;
  }

  /* Ensure buttons, inputs, and other form elements inherit fonts */
  button, input, textarea, select {
    font-family: inherit;
  }

  /* Force all text elements to use Poppins */
  p, span, div, a, label, li, td, th {
    font-family: inherit;
  }
}

/* Component-specific font overrides */
@layer components {
  /* Ensure Relume UI components use Poppins */
  [class*="relume"],
  [id*="relume"],
  .relume-button,
  .relume-input,
  .relume-text {
    font-family: var(--font-heading), 'Poppins', sans-serif;
  }

  /* Ensure all buttons use Poppins */
  button,
  [role="button"],
  .btn,
  [class*="button"],
  [class*="btn"] {
    font-family: var(--font-heading), 'Poppins', sans-serif !important;
  }

  /* Ensure navigation elements use Poppins */
  nav,
  nav a,
  nav button,
  [role="navigation"],
  [role="navigation"] a,
  [role="navigation"] button {
    font-family: var(--font-heading), 'Poppins', sans-serif;
  }
}



