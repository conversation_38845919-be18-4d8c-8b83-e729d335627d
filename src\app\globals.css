/* styles/globals.css */

/* Import theme files - must come before @tailwind directives */
@import './themes/homepage.css';
@import './themes/forestforward.css';
@import './themes/lagom.css';
@import './themes/storyforward.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base font styles - ensure all text uses custom fonts */
@layer base {
  /* Set default body font to Space Grotesk */
  body {
    font-family: var(--font-body), 'Space Grotesk', sans-serif;
  }

  /* Set all headings to use Poppins */
  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading), 'Poppins', sans-serif;
  }

  /* Ensure buttons, inputs, and other form elements inherit fonts */
  button, input, textarea, select {
    font-family: inherit;
  }

  /* Force all text elements to use custom fonts */
  p, span, div, a, label, li, td, th {
    font-family: inherit;
  }
}

/* Component-specific font overrides */
@layer components {
  /* Ensure Relume UI components use custom fonts */
  [class*="relume"],
  [id*="relume"],
  .relume-button,
  .relume-input,
  .relume-text {
    font-family: var(--font-body), 'Space Grotesk', sans-serif;
  }

  /* Ensure all buttons use Space Grotesk */
  button,
  [role="button"],
  .btn,
  [class*="button"],
  [class*="btn"] {
    font-family: var(--font-body), 'Space Grotesk', sans-serif !important;
  }

  /* Ensure navigation elements use Space Grotesk */
  nav,
  nav a,
  nav button,
  [role="navigation"],
  [role="navigation"] a,
  [role="navigation"] button {
    font-family: var(--font-body), 'Space Grotesk', sans-serif;
  }
}



